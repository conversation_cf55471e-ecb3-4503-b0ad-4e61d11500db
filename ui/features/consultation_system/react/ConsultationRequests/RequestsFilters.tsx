import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Button } from '@instructure/ui-buttons'
import { TextInput } from '@instructure/ui-text-input'
import { Select } from '@instructure/ui-select'
import { Grid } from '@instructure/ui-grid'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import type { ConsultationFilters } from '../types'

interface RequestsFiltersProps {
  filters: ConsultationFilters
  concernTypes: string[]
  statuses: string[]
  userRole: 'student' | 'faculty'
  onFiltersChange: (filters: ConsultationFilters) => void
  loading: boolean
}

const RequestsFilters: React.FC<RequestsFiltersProps> = ({
  filters,
  concernTypes,
  statuses,
  userRole,
  onFiltersChange,
  loading
}) => {
  const [localFilters, setLocalFilters] = useState<ConsultationFilters>(filters)

  const handleFilterChange = (key: keyof ConsultationFilters, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
  }

  const handleClearFilters = () => {
    const clearedFilters = {}
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }

  const hasActiveFilters = Object.values(localFilters).some(value => value && value !== '')

  console.log('REQUEST FILTERS concernTypes', concernTypes)

  return (
    <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="0 0 medium 0">
      <Grid>
        <Grid.Row>
          <Grid.Col width={3}>
            <Select
              renderLabel="Status"
              placeholder="All statuses"
              value={localFilters.status || ''}
              onChange={(e, { value }) => handleFilterChange('status', value as string)}
            >
              <Select.Option id="all-statuses" value="">
                All Statuses
              </Select.Option>
              {statuses.map(status => (
                <Select.Option key={status} id={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Select.Option>
              ))}
            </Select>
          </Grid.Col>
          
          <Grid.Col width={3}>
            <Select
              renderLabel="Concern Type test"
              placeholder="All types"
              value={localFilters.concern_type || ''}
              onChange={(e: any, { value }: any) => handleFilterChange('concern_type', value as string)}
            >
              <Select.Option id="all-types" value="">
                All Types
              </Select.Option>
              {concernTypes.map((type, index) => (
                <Select.Option key={index} id={type} value={type}>
                  {type}
                </Select.Option>
              ))}
            </Select>
          </Grid.Col>
          
          <Grid.Col width={3}>
            <TextInput
              renderLabel={userRole === 'student' ? 'Search Faculty' : 'Search Students'}
              placeholder={userRole === 'student' ? 'Faculty name' : 'Student name or ID'}
              value={localFilters.student_search || ''}
              onChange={(e) => handleFilterChange('student_search', e.target.value)}
            />
          </Grid.Col>
          
          <Grid.Col width={3}>
            <View as="div" display="flex" gap="small" alignItems="end" height="100%">
              <Button
                color="primary"
                renderIcon={IconSearchLine}
                onClick={handleApplyFilters}
                disabled={loading}
              >
                Search
              </Button>
              {hasActiveFilters && (
                <Button
                  renderIcon={IconXLine}
                  onClick={handleClearFilters}
                  disabled={loading}
                >
                  Clear
                </Button>
              )}
            </View>
          </Grid.Col>
        </Grid.Row>
        
        <Grid.Row>
          <Grid.Col width={3}>
            <View as="div">
              <label htmlFor="requests-start-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
                Start Date
              </label>
              <input
                id="requests-start-date-input"
                type="date"
                value={localFilters.start_date || ''}
                onChange={(e) => handleFilterChange('start_date', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #C7CDD1',
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  backgroundColor: '#FFFFFF'
                }}
              />
            </View>
          </Grid.Col>

          <Grid.Col width={3}>
            <View as="div">
              <label htmlFor="requests-end-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
                End Date
              </label>
              <input
                id="requests-end-date-input"
                type="date"
                value={localFilters.end_date || ''}
                onChange={(e) => handleFilterChange('end_date', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #C7CDD1',
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  backgroundColor: '#FFFFFF'
                }}
              />
            </View>
          </Grid.Col>
          
          <Grid.Col width={6}>
            <View as="div" padding="small 0 0 0">
              <View as="div" display="flex" gap="medium" alignItems="center">
                <label>
                  <input
                    type="checkbox"
                    checked={localFilters.urgent === 'true'}
                    onChange={(e) => handleFilterChange('urgent', e.target.checked ? 'true' : '')}
                  />
                  <span style={{ marginLeft: '0.5rem' }}>Urgent Requests</span>
                </label>
                <label>
                  <input
                    type="checkbox"
                    checked={localFilters.upcoming === 'true'}
                    onChange={(e) => handleFilterChange('upcoming', e.target.checked ? 'true' : '')}
                  />
                  <span style={{ marginLeft: '0.5rem' }}>Upcoming This Week</span>
                </label>
              </View>
            </View>
          </Grid.Col>
        </Grid.Row>
      </Grid>
    </View>
  )
}

export default RequestsFilters
